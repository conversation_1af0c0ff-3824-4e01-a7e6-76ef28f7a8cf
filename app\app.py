from flask import Flask, render_template, request, jsonify, Response, session, redirect, url_for, flash
from datetime import datetime, timedelta, timezone
import os
import csv
from io import StringIO
from dotenv import load_dotenv
from supabase import create_client, Client
from functools import wraps
import hashlib

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'your-secret-key-change-this-in-production')

# Define IST timezone
IST = timezone(timedelta(hours=5, minutes=30))

def get_ist_now():
    """Get current time in IST"""
    return datetime.now(IST)

def convert_to_ist(dt_string):
    """Convert ISO string to IST datetime"""
    try:
        # Parse ISO string with proper timezone handling
        if dt_string.endswith('Z'):
            # This is from Arduino RTC which is ALREADY in IST, but marked as Z
            # So we treat it as IST directly (no conversion needed)
            dt = datetime.fromisoformat(dt_string.replace('Z', ''))
            # Add IST timezone info
            dt = dt.replace(tzinfo=IST)
            return dt
        elif '+05:30' in dt_string:
            # Already IST timestamp
            dt = datetime.fromisoformat(dt_string)
            return dt
        elif dt_string.endswith('+00:00'):
            # True UTC timestamp - convert to IST
            dt = datetime.fromisoformat(dt_string)
            return dt.astimezone(IST)
        else:
            # No timezone info - assume it's already IST
            dt = datetime.fromisoformat(dt_string)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=IST)
            return dt
    except Exception as e:
        print(f"Error converting timestamp {dt_string}: {e}")
        return None

def format_ist_datetime(dt):
    """Format datetime in IST format"""
    if isinstance(dt, str):
        dt = convert_to_ist(dt)
    if dt:
        return dt.strftime('%Y-%m-%d %H:%M:%S IST')
    return None

def ist_to_iso(dt):
    """Convert IST datetime to ISO string for database storage"""
    if isinstance(dt, datetime):
        return dt.isoformat()
    return dt

# Initialize Supabase client
try:
    supabase: Client = create_client(
        'https://zvfltkbciwppawghqpdl.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2Zmx0a2JjaXdwcGF3Z2hxcGRsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MDI0NjYsImV4cCI6MjA2NjE3ODQ2Nn0.z_U2VOsbP1lMK88dDp7xmVTLkQQdO9hvI8s47JfpspE'
    )
except Exception as e:
    print(f"Supabase initialization failed: {e}")
    supabase = None

# API key for authentication
API_KEY = os.getenv('API_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2Zmx0a2JjaXdwcGF3Z2hxcGRsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MDI0NjYsImV4cCI6MjA2NjE3ODQ2Nn0.z_U2VOsbP1lMK88dDp7xmVTLkQQdO9hvI8s47JfpspE')

# User authentication system
# You can customize usernames and passwords here
USERS = {
    # Admin user - sees all machines
    'admin': {
        'password': hashlib.sha256('admin123'.encode()).hexdigest(),
        'machine_ids': ['machine_1', 'machine_2', 'machine_3'],  # Admin sees all machines
        'machine_name': 'All Machines',
        'role': 'admin'
    },

    # Custom users - you can modify these or add new ones
    'pankaj': {
        'password': hashlib.sha256('pankaj123'.encode()).hexdigest(),
        'machine_ids': ['machine_1', 'machine_2', 'machine_3'],
        'machine_name': 'All Machines',
        'role': 'admin'
    },
     'abhi': {
        'password': hashlib.sha256('abhi123'.encode()).hexdigest(),
        'machine_ids': ['machine_1', 'machine_2', 'machine_3'],
        'machine_name': 'All Machines',
        'role': 'admin'
    },
    'ankur_admin': {
        'password': hashlib.sha256('ankur@2023'.encode()).hexdigest(),
        'machine_ids': ['machine_1', 'machine_2', 'machine_3'],
        'machine_name': 'All Production Lines',
        'role': 'admin'
    },
    'production_manager': {
        'password': hashlib.sha256('prod@123'.encode()).hexdigest(),
        'machine_ids': ['machine_1', 'machine_2'],
        'machine_name': 'Production Lines A & B',
        'role': 'user'
    },
    'line_a_operator': {
        'password': hashlib.sha256('lineA@2023'.encode()).hexdigest(),
        'machine_ids': ['machine_1'],
        'machine_name': 'Production Line A',
        'role': 'user'
    },
    'line_b_operator': {
        'password': hashlib.sha256('lineB@2023'.encode()).hexdigest(),
        'machine_ids': ['machine_2'],
        'machine_name': 'Production Line B',
        'role': 'user'
    },
    'line_c_operator': {
        'password': hashlib.sha256('lineC@2023'.encode()).hexdigest(),
        'machine_ids': ['machine_3'],
        'machine_name': 'Production Line C',
        'role': 'user'
    },

    # Keep original demo users for testing
    'user1': {
        'password': hashlib.sha256('password1'.encode()).hexdigest(),
        'machine_ids': ['machine_1'],
        'machine_name': 'Production Line A',
        'role': 'user'
    },
    'user2': {
        'password': hashlib.sha256('password2'.encode()).hexdigest(),
        'machine_ids': ['machine_2'],
        'machine_name': 'Production Line B',
        'role': 'user'
    },
    'user3': {
        'password': hashlib.sha256('password3'.encode()).hexdigest(),
        'machine_ids': ['machine_3'],
        'machine_name': 'Production Line C',
        'role': 'user'
    }
}

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def get_user_machine_filter():
    """Get machine filter for current user"""
    if 'username' not in session:
        return []

    user = USERS.get(session['username'])
    if not user:
        return []

    return user['machine_ids']

def add_user(username, password, machine_ids, machine_name, role='user'):
    """Helper function to add new users easily"""
    USERS[username] = {
        'password': hashlib.sha256(password.encode()).hexdigest(),
        'machine_ids': machine_ids,
        'machine_name': machine_name,
        'role': role
    }
    print(f"✅ User '{username}' added successfully with access to {machine_name}")

def generate_password_hash(password):
    """Helper function to generate password hash for manual addition"""
    return hashlib.sha256(password.encode()).hexdigest()

@app.context_processor
def inject_now():
    return {'now': get_ist_now()}

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        # Hash the provided password
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        # Check credentials
        if username in USERS and USERS[username]['password'] == password_hash:
            session['username'] = username
            session['machine_ids'] = USERS[username]['machine_ids']
            session['machine_name'] = USERS[username]['machine_name']
            session['role'] = USERS[username]['role']

            flash(f'Welcome back, {username}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password', 'error')

    return render_template('login.html')

@app.route('/logout')
def logout():
    username = session.get('username', 'User')
    session.clear()
    flash(f'Goodbye, {username}! You have been logged out.', 'info')
    return redirect(url_for('login'))

@app.route('/favicon.ico')
def favicon():
    # Simple 16x16 favicon data (base64 encoded)
    favicon_data = b'\x00\x00\x01\x00\x01\x00\x10\x10\x00\x00\x01\x00\x08\x00h\x05\x00\x00\x16\x00\x00\x00(\x00\x00\x00\x10\x00\x00\x00 \x00\x00\x00\x01\x00\x08\x00\x00\x00\x00\x00@\x05\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x80\x00\x00\x80\x00\x00\x00\x80\x80\x00\x80\x00\x00\x00\x80\x00\x80\x00\x80\x80\x00\x00\x80\x80\x80\x00\xc0\xc0\xc0\x00\x00\x00\xff\x00\x00\xff\x00\x00\x00\xff\xff\x00\xff\x00\x00\x00\xff\x00\xff\x00\xff\xff\x00\x00\xff\xff\xff\x00'
    return Response(favicon_data, mimetype='image/x-icon')

@app.route('/')
@login_required
def index():
    return redirect(url_for('dashboard'))

@app.route('/dashboard')
@login_required
def dashboard():
    return render_template('dashboard.html', now=get_ist_now())

@app.route('/events')
@login_required
def events():
    return render_template('events.html')

@app.route('/test')
@login_required
def test_page():
    return render_template('test.html')

@app.route('/profile')
@login_required
def profile():
    user_info = USERS.get(session['username'], {})
    return render_template('profile.html', user_info=user_info)

@app.route('/settings')
@login_required
def settings():
    return render_template('settings.html')

@app.route('/admin/users')
@login_required
def admin_users():
    # Only admin can access user management
    if session.get('role') != 'admin':
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('dashboard'))

    # Get all users (excluding passwords for security)
    users_info = {}
    for username, user_data in USERS.items():
        users_info[username] = {
            'machine_ids': user_data['machine_ids'],
            'machine_name': user_data['machine_name'],
            'role': user_data['role']
        }

    return render_template('admin_users.html', users=users_info)

@app.route('/api/admin/add-user', methods=['POST'])
@login_required
def api_add_user():
    # Only admin can add users
    if session.get('role') != 'admin':
        return jsonify({'error': 'Access denied'}), 403

    data = request.get_json()
    try:
        username = data['username']
        password = data['password']
        machine_ids = data['machine_ids']
        machine_name = data['machine_name']
        role = data.get('role', 'user')

        # Check if user already exists
        if username in USERS:
            return jsonify({'error': 'Username already exists'}), 400

        # Add the user
        add_user(username, password, machine_ids, machine_name, role)

        return jsonify({'message': f'User {username} added successfully'}), 200

    except KeyError as e:
        return jsonify({'error': f'Missing field: {e}'}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/test-ist')
def test_ist():
    """Test IST time conversion"""
    try:
        current_utc = datetime.now(timezone.utc)
        current_ist = get_ist_now()

        # Test conversion of different timestamp formats
        test_cases = [
            "2023-12-25T10:30:45.000Z",  # UTC with Z
            "2023-12-25T16:00:45+05:30",  # IST from Arduino
            "2023-12-25T10:30:45+00:00",  # UTC explicit
            "2023-12-25T10:30:45"         # No timezone
        ]

        conversions = {}
        for test_case in test_cases:
            converted = convert_to_ist(test_case)
            formatted = format_ist_datetime(test_case)
            conversions[test_case] = {
                'converted_ist': converted.isoformat() if converted else None,
                'formatted': formatted
            }

        return jsonify({
            'current_utc': current_utc.isoformat(),
            'current_ist': current_ist.isoformat(),
            'current_ist_formatted': format_ist_datetime(current_ist.isoformat()),
            'test_conversions': conversions,
            'timezone_info': 'IST = UTC + 5:30',
            'arduino_format': 'Should send: YYYY-MM-DDTHH:MM:SS+05:30'
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/supabase/setup')
def setup_supabase():
    """Setup Supabase table structure and provide info"""
    try:
        # Test connection to Supabase
        test_response = supabase.table('machine_events').select("count", count="exact").limit(1).execute()

        # Get table info
        sample_data = supabase.table('machine_events').select("*").limit(5).execute()

        # Count events by machine
        machine_counts = {}
        for machine_id in ['machine_1', 'machine_2', 'machine_3']:
            count_resp = supabase.table('machine_events') \
                .select("*", count="exact") \
                .eq('machine_id', machine_id) \
                .execute()
            machine_counts[machine_id] = count_resp.count

        return jsonify({
            'status': 'Connected to Supabase successfully',
            'table': 'machine_events',
            'total_records': test_response.count,
            'machine_counts': machine_counts,
            'sample_data': sample_data.data[:3] if sample_data.data else [],
            'table_structure': {
                'columns': [
                    'id (auto-generated)',
                    'start_time (timestamp)',
                    'end_time (timestamp)',
                    'duration (integer seconds)',
                    'machine_id (text)',
                    'created_at (auto-generated)'
                ]
            },
            'machine_mapping': {
                'machine_1': 'Production Line A',
                'machine_2': 'Production Line B',
                'machine_3': 'Production Line C'
            },
            'arduino_payload_example': {
                'start_time': '2023-12-25T10:30:45+05:30',
                'end_time': '2023-12-25T10:35:30+05:30',
                'duration': 285,
                'machine_id': 'machine_1',
                'api_key': 'your_api_key'
            }
        }), 200
    except Exception as e:
        return jsonify({
            'error': 'Supabase connection failed',
            'details': str(e),
            'setup_instructions': {
                'step1': 'Ensure Supabase project is active',
                'step2': 'Create table "machine_events" with columns: id, start_time, end_time, duration, machine_id, created_at',
                'step3': 'Set up RLS policies if needed',
                'step4': 'Verify API keys are correct'
            }
        }), 500

@app.route('/api/test-logo')
def test_logo():
    """Test logo file existence and provide debugging info"""
    import os
    try:
        # Check if logo file exists
        logo_path = os.path.join(app.static_folder, 'images', 'logo.jpeg')
        logo_exists = os.path.exists(logo_path)

        # Get file info if exists
        file_info = {}
        if logo_exists:
            file_info = {
                'size': os.path.getsize(logo_path),
                'path': logo_path,
                'readable': os.access(logo_path, os.R_OK)
            }

        # Check static folder structure
        static_folder = app.static_folder
        images_folder = os.path.join(static_folder, 'images')

        return jsonify({
            'logo_file_exists': logo_exists,
            'logo_path': logo_path,
            'file_info': file_info,
            'static_folder': static_folder,
            'images_folder_exists': os.path.exists(images_folder),
            'images_folder_contents': os.listdir(images_folder) if os.path.exists(images_folder) else [],
            'url_for_logo': url_for('static', filename='images/logo.jpeg'),
            'instructions': {
                'step1': 'Place your logo file at: ' + logo_path,
                'step2': 'Ensure file is named exactly: logo.jpeg',
                'step3': 'Restart Flask server after adding file',
                'step4': 'Check this endpoint again to verify'
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e), 'message': 'Error checking logo file'}), 500

# CSV Export Helper Functions
def generate_events_csv(events):
    """Generate CSV data for events"""
    csv_data = StringIO()
    writer = csv.writer(csv_data)

    # Write header
    writer.writerow(['Event ID', 'Start Time (IST)', 'End Time (IST)', 'Duration (s)', 'Duration (min)'])

    # Write data
    for event in events:
        writer.writerow([
            event['id'],
            format_ist_datetime(event['start_time']),
            format_ist_datetime(event['end_time']),
            event['duration'],
            round(event['duration'] / 60, 2)
        ])

    return csv_data.getvalue()

def generate_summary_csv(summary):
    """Generate CSV data for summary"""
    csv_data = StringIO()
    writer = csv.writer(csv_data)

    writer.writerow(['Metric', 'Value'])
    writer.writerow(['Total Events', summary['total_events']])
    writer.writerow(['Total Runtime (hours)', round(summary['total_runtime'] / 3600, 2)])

    if summary['last_event']:
        writer.writerow(['Last Event Start (IST)', format_ist_datetime(summary['last_event']['start_time'])])
        writer.writerow(['Last Event End (IST)', format_ist_datetime(summary['last_event']['end_time'])])
        writer.writerow(['Last Event Duration (min)', round(summary['last_event']['duration'] / 60, 2)])

    return csv_data.getvalue()

def generate_chart_csv(labels, data, title):
    """Generate CSV data for chart datasets"""
    csv_data = StringIO()
    writer = csv.writer(csv_data)
    
    writer.writerow([title, 'Value'])
    for i in range(len(labels)):
        writer.writerow([labels[i], data[i]])
    
    return csv_data.getvalue()

@app.route('/api/test-data', methods=['POST'])
def send_test_data():
    """Send demo data for testing"""
    try:
        # Generate some test events using IST
        now = get_ist_now()
        test_events = []

        # Create 5 test events with different durations
        for i in range(5):
            start_time = now - timedelta(minutes=30 + i*10)
            duration = [300, 600, 900, 1200, 1800][i]  # 5, 10, 15, 20, 30 minutes
            end_time = start_time + timedelta(seconds=duration)

            event_data = {
                "start_time": ist_to_iso(start_time),
                "end_time": ist_to_iso(end_time),
                "duration": duration
            }

            # Save to database
            response = supabase.table('machine_events').insert(event_data).execute()
            if response.data:
                test_events.append(event_data)

        return jsonify({
            'message': f'Successfully added {len(test_events)} test events',
            'events': test_events
        }), 200

    except Exception as e:
        return jsonify({'error': 'Failed to add test data', 'details': str(e)}), 500

@app.route('/api/test-hardware', methods=['POST'])
def test_hardware():
    """Special endpoint for testing hardware integration"""
    try:
        data = request.get_json()

        # Detailed validation for hardware
        validation_results = {
            'status': 'success',
            'message': 'Hardware test successful',
            'validations': [],
            'warnings': [],
            'data_received': data
        }

        # Check required fields
        required_fields = ['start_time', 'end_time', 'duration']
        for field in required_fields:
            if field not in data:
                validation_results['validations'].append(f"❌ Missing required field: {field}")
                validation_results['status'] = 'error'
            else:
                validation_results['validations'].append(f"✅ Field '{field}' present")

        # Validate data types and formats
        if 'duration' in data:
            try:
                duration = int(data['duration'])
                if duration <= 0:
                    validation_results['warnings'].append(f"⚠️ Duration is {duration}, should be positive")
                elif duration > 86400:  # More than 24 hours
                    validation_results['warnings'].append(f"⚠️ Duration is {duration}s (>24h), seems unusual")
                else:
                    validation_results['validations'].append(f"✅ Duration {duration}s is valid")
            except ValueError:
                validation_results['validations'].append(f"❌ Duration must be a number")
                validation_results['status'] = 'error'

        # Validate timestamps
        for time_field in ['start_time', 'end_time']:
            if time_field in data:
                try:
                    datetime.fromisoformat(data[time_field].replace('Z', '+00:00'))
                    validation_results['validations'].append(f"✅ {time_field} format is valid")
                except ValueError:
                    validation_results['validations'].append(f"❌ {time_field} format is invalid")
                    validation_results['status'] = 'error'

        # If validation passed, try to save to database
        if validation_results['status'] == 'success':
            try:
                # Prepare data for database
                start_time = data['start_time']
                end_time = data['end_time']
                duration = int(data['duration'])

                event_data = {
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": duration
                }

                # Save to database
                response = supabase.table('machine_events').insert(event_data).execute()

                if response.data:
                    validation_results['validations'].append("✅ Data saved to database successfully")
                    validation_results['database_record'] = response.data[0]
                else:
                    validation_results['validations'].append("❌ Failed to save to database")
                    validation_results['status'] = 'error'

            except Exception as db_error:
                validation_results['validations'].append(f"❌ Database error: {str(db_error)}")
                validation_results['status'] = 'error'

        # Return detailed results
        status_code = 200 if validation_results['status'] == 'success' else 400
        return jsonify(validation_results), status_code

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': 'Hardware test failed',
            'error': str(e),
            'validations': [f"❌ Exception occurred: {str(e)}"]
        }), 500

@app.route('/api/machine-data', methods=['POST'])
def receive_machine_data():
    # Get data from request
    data = request.get_json()

    print(f"📥 Received data: {data}")  # Debug log

    # Validate API key
    if data.get('api_key') != API_KEY:
        return jsonify({'error': 'Invalid API key'}), 401

    # Extract data
    try:
        start_time = data['start_time']
        end_time = data['end_time']
        duration = int(data['duration'])

        # Determine machine ID based on some criteria (you can modify this logic)
        # For now, we'll use a simple mapping or default to machine_1
        machine_id = data.get('machine_id', 'machine_1')  # Default to machine_1

        print(f"🕐 Timestamps received:")
        print(f"   Start: {start_time}")
        print(f"   End: {end_time}")
        print(f"   Machine ID: {machine_id}")

        # Convert IST timestamps to proper format for database storage
        start_ist = convert_to_ist(start_time)
        end_ist = convert_to_ist(end_time)

        if start_ist and end_ist:
            # Store as ISO format (database will handle timezone)
            start_time_db = start_ist.isoformat()
            end_time_db = end_ist.isoformat()

            print(f"🕐 Converted for database:")
            print(f"   Start: {start_time_db}")
            print(f"   End: {end_time_db}")
        else:
            # Fallback to original timestamps
            start_time_db = start_time
            end_time_db = end_time

    except (KeyError, ValueError) as e:
        return jsonify({'error': 'Invalid data format', 'details': str(e)}), 400

    # Save to Supabase with machine ID
    try:
        response = supabase.table('machine_events').insert({
            "start_time": start_time_db,
            "end_time": end_time_db,
            "duration": duration,
            "machine_id": machine_id
        }).execute()

        if response.data:
            print(f"✅ Data saved successfully to database for {machine_id}")
            return jsonify({'message': 'Data saved successfully'}), 200
        else:
            return jsonify({'error': 'Supabase insert failed'}), 500
    except Exception as e:
        print(f"❌ Database error: {e}")
        return jsonify({'error': 'Supabase error', 'details': str(e)}), 500

@app.route('/api/machine-data', methods=['GET'])
@login_required
def get_machine_data():
    # Get events from Supabase filtered by user's machines
    try:
        print(f"🔍 Loading events for user: {session.get('username')} (role: {session.get('role')})")

        # Get user's machine filter
        machine_ids = get_user_machine_filter()
        print(f"🏭 User has access to machines: {machine_ids}")

        # Check if supabase is initialized
        if supabase is None:
            print("❌ Supabase client not initialized")
            return jsonify({'error': 'Database connection not available'}), 500

        # Query Supabase with machine filter
        query = supabase.table('machine_events').select("*")

        # Apply machine filter if user is not admin
        if session.get('role') != 'admin':
            if not machine_ids:
                print("⚠️ User has no machine access")
                return jsonify([]), 200
            query = query.in_('machine_id', machine_ids)
            print(f"🔍 Filtering by machine IDs: {machine_ids}")
        else:
            print("👑 Admin user - showing all machines")

        response = query.order("start_time", desc=True).execute()
        print(f"📊 Found {len(response.data)} events in database")

        # Add machine info to each event for better display
        events_with_info = []
        machine_name_map = {
            'machine_1': 'Production Line A',
            'machine_2': 'Production Line B',
            'machine_3': 'Production Line C'
        }

        for event in response.data:
            event_copy = event.copy()
            event_copy['machine_name'] = machine_name_map.get(event['machine_id'], event['machine_id'])
            events_with_info.append(event_copy)

        print(f"✅ Returning {len(events_with_info)} events to frontend")
        return jsonify(events_with_info), 200

    except Exception as e:
        print(f"❌ Error in get_machine_data: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

@app.route('/api/machine-data/<machine_id>', methods=['GET'])
@login_required
def get_specific_machine_data(machine_id):
    """Get data for a specific machine"""
    try:
        # Check if user has access to this machine
        user_machines = get_user_machine_filter()
        if session.get('role') != 'admin' and machine_id not in user_machines:
            return jsonify({'error': 'Access denied to this machine'}), 403

        # Query Supabase for specific machine
        response = supabase.table('machine_events') \
            .select("*") \
            .eq('machine_id', machine_id) \
            .order("start_time", desc=True) \
            .execute()

        machine_name_map = {
            'machine_1': 'Production Line A',
            'machine_2': 'Production Line B',
            'machine_3': 'Production Line C'
        }

        events_with_info = []
        for event in response.data:
            event_copy = event.copy()
            event_copy['machine_name'] = machine_name_map.get(machine_id, machine_id)
            events_with_info.append(event_copy)

        return jsonify({
            'machine_id': machine_id,
            'machine_name': machine_name_map.get(machine_id, machine_id),
            'total_events': len(events_with_info),
            'events': events_with_info
        }), 200
    except Exception as e:
        return jsonify({'error': 'Failed to fetch machine data', 'details': str(e)}), 500

@app.route('/api/machines/summary', methods=['GET'])
@login_required
def get_machines_summary():
    """Get summary for all accessible machines"""
    try:
        user_machines = get_user_machine_filter()
        machine_name_map = {
            'machine_1': 'Production Line A',
            'machine_2': 'Production Line B',
            'machine_3': 'Production Line C'
        }

        machines_summary = []

        for machine_id in user_machines:
            # Get events count for this machine
            count_response = supabase.table('machine_events') \
                .select("*", count="exact") \
                .eq('machine_id', machine_id) \
                .execute()

            # Get total runtime for this machine
            runtime_response = supabase.table('machine_events') \
                .select("duration") \
                .eq('machine_id', machine_id) \
                .execute()

            total_runtime = sum(event['duration'] for event in runtime_response.data) if runtime_response.data else 0

            # Get last event for this machine
            last_event_response = supabase.table('machine_events') \
                .select("*") \
                .eq('machine_id', machine_id) \
                .order("start_time", desc=True) \
                .limit(1) \
                .execute()

            last_event = last_event_response.data[0] if last_event_response.data else None

            machines_summary.append({
                'machine_id': machine_id,
                'machine_name': machine_name_map.get(machine_id, machine_id),
                'total_events': count_response.count,
                'total_runtime': total_runtime,
                'last_event': last_event
            })

        return jsonify({
            'user': session['username'],
            'role': session['role'],
            'accessible_machines': len(user_machines),
            'machines': machines_summary
        }), 200
    except Exception as e:
        return jsonify({'error': 'Failed to fetch machines summary', 'details': str(e)}), 500

# CSV Export Endpoint for Full Events Data
@app.route('/api/export/events-csv')
@login_required
def export_events_csv():
    try:
        # Get user's machine filter
        machine_ids = get_user_machine_filter()

        # Query Supabase with machine filter
        query = supabase.table('machine_events').select("*")

        # Apply machine filter if user is not admin
        if session.get('role') != 'admin':
            query = query.in_('machine_id', machine_ids)

        response = query.order("start_time", desc=True).execute()

        events = response.data

        if not events:
            return jsonify({'error': 'No events found'}), 404

        csv_data = generate_events_csv(events)

        # Include machine info in filename
        machine_suffix = session.get('machine_name', 'Machine').replace(' ', '_')
        filename = f"machine_events_{machine_suffix}_{get_ist_now().strftime('%Y%m%d_%H%M%S')}_IST.csv"

        return Response(
            csv_data,
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename={filename}'}
        )

    except Exception as e:
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

@app.route('/api/summary')
@login_required
def get_summary():
    try:
        # Get user's machine filter
        machine_ids = get_user_machine_filter()

        # Build base query with machine filter
        base_query = supabase.table('machine_events')

        # Apply machine filter if user is not admin
        if session.get('role') != 'admin':
            base_query = base_query.in_('machine_id', machine_ids)

        # Total events
        count_resp = base_query.select("count", count="exact").execute()
        total_events = count_resp.count

        # Total runtime - calculate manually since RPC might not support filtering
        events_resp = base_query.select("duration").execute()
        total_runtime = sum(event['duration'] for event in events_resp.data) if events_resp.data else 0

        # Last event
        last_event_query = supabase.table('machine_events').select("*")
        if session.get('role') != 'admin':
            last_event_query = last_event_query.in_('machine_id', machine_ids)

        last_event_resp = last_event_query.order("start_time", desc=True).limit(1).execute()
        last_event = last_event_resp.data[0] if last_event_resp.data else None

        return jsonify({
            'total_events': total_events,
            'total_runtime': total_runtime,
            'last_event': last_event,
            'machine_info': {
                'name': session.get('machine_name', 'Unknown'),
                'ids': machine_ids,
                'role': session.get('role', 'user')
            }
        }), 200
    except Exception as e:
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

# CSV Export Endpoint for Summary Data
@app.route('/api/export/summary-csv')
def export_summary_csv():
    try:
        # Get summary data
        count_resp = supabase.table('machine_events').select("count", count="exact").execute()
        total_events = count_resp.count
        runtime_resp = supabase.rpc('get_total_runtime', {}).execute()
        total_runtime = runtime_resp.data[0]['total'] if runtime_resp.data else 0
        last_event_resp = supabase.table('machine_events').select("*").order("start_time", desc=True).limit(1).execute()
        last_event = last_event_resp.data[0] if last_event_resp.data else None

        summary = {
            'total_events': total_events,
            'total_runtime': total_runtime,
            'last_event': last_event
        }
        
        csv_data = generate_summary_csv(summary)
        filename = f"machine_summary_{get_ist_now().strftime('%Y%m%d_%H%M%S')}_IST.csv"
        
        return Response(
            csv_data,
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename={filename}'}
        )
        
    except Exception as e:
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

@app.route('/api/charts/daily-runtime')
@login_required
def get_daily_runtime():
    try:
        # Check if Supabase is available
        if supabase is None:
            return jsonify({'error': 'Database connection not available'}), 500
        # Get last 7 days of data in IST
        end_date = get_ist_now()
        start_date = end_date - timedelta(days=7)

        response = supabase.table('machine_events') \
            .select("start_time, end_time, duration") \
            .gte("start_time", start_date.isoformat()) \
            .lte("start_time", end_date.isoformat()) \
            .execute()

        # Group by day and calculate total runtime (using IST dates)
        daily_data = {}
        for event in response.data:
            # Convert to IST and get date
            ist_datetime = convert_to_ist(event['start_time'])
            if ist_datetime:
                event_date = ist_datetime.date()
                day_key = event_date.strftime('%Y-%m-%d')

                if day_key not in daily_data:
                    daily_data[day_key] = 0
                daily_data[day_key] += event['duration']

        # Create labels and data for last 7 days 
        labels = []
        data = []
        for i in range(6, -1, -1):
            date = (end_date - timedelta(days=i)).date()
            day_key = date.strftime('%Y-%m-%d')
            labels.append(date.strftime('%a %m/%d'))
            data.append(round(daily_data.get(day_key, 0) / 3600, 2))  # Convert to hours

        return jsonify({
            'labels': labels,
            'data': data
        }), 200
    except Exception as e:
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

# CSV Export Endpoint for Daily Runtime
@app.route('/api/export/daily-runtime-csv')
def export_daily_runtime_csv():
    try:
        daily_data = get_daily_runtime().get_json()
        csv_data = generate_chart_csv(
            daily_data['labels'], 
            daily_data['data'],
            'Daily Runtime (hours)'
        )
        
        filename = f"daily_runtime_{get_ist_now().strftime('%Y%m%d_%H%M%S')}_IST.csv"
        
        return Response(
            csv_data,
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename={filename}'}
        )
        
    except Exception as e:
        return jsonify({'error': 'Export failed', 'details': str(e)}), 500

@app.route('/api/charts/hourly-distribution')
@login_required
def get_hourly_distribution():
    try:
        # Check if Supabase is available
        if supabase is None:
            return jsonify({'error': 'Database connection not available'}), 500
        # Get events from last 30 days (IST)
        end_date = get_ist_now()
        start_date = end_date - timedelta(days=30)

        response = supabase.table('machine_events') \
            .select("start_time, duration") \
            .gte("start_time", start_date.isoformat()) \
            .execute()

        # Group by hour of day (IST hours)
        hourly_data = [0] * 24
        for event in response.data:
            # Convert to IST and get hour
            ist_datetime = convert_to_ist(event['start_time'])
            if ist_datetime:
                hour = ist_datetime.hour
                hourly_data[hour] += event['duration'] / 3600  

        labels = [f"{i:02d}:00 IST" for i in range(24)]

        return jsonify({
            'labels': labels,
            'data': [round(x, 2) for x in hourly_data]
        }), 200
    except Exception as e:
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

# CSV Export Endpoint for Hourly Distribution
@app.route('/api/export/hourly-distribution-csv')
def export_hourly_distribution_csv():
    try:
        hourly_data = get_hourly_distribution().get_json()
        csv_data = generate_chart_csv(
            hourly_data['labels'], 
            hourly_data['data'],
            'Hourly Runtime (hours)'
        )
        
        filename = f"hourly_distribution_{get_ist_now().strftime('%Y%m%d_%H%M%S')}_IST.csv"
        
        return Response(
            csv_data,
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename={filename}'}
        )
        
    except Exception as e:
        return jsonify({'error': 'Export failed', 'details': str(e)}), 500

@app.route('/api/charts/duration-distribution')
@login_required
def get_duration_distribution():
    try:
        # Check if Supabase is available
        if supabase is None:
            return jsonify({'error': 'Database connection not available'}), 500
        response = supabase.table('machine_events') \
            .select("duration") \
            .execute()

        # Categorize durations
        categories = {
            '< 1 min': 0,
            '1-5 min': 0,
            '5-15 min': 0,
            '15-30 min': 0,
            '30-60 min': 0,
            '> 1 hour': 0
        }

        for event in response.data:
            duration_min = event['duration'] / 60
            if duration_min < 1:
                categories['< 1 min'] += 1
            elif duration_min < 5:
                categories['1-5 min'] += 1
            elif duration_min < 15:
                categories['5-15 min'] += 1
            elif duration_min < 30:
                categories['15-30 min'] += 1
            elif duration_min < 60:
                categories['30-60 min'] += 1
            else:
                categories['> 1 hour'] += 1

        return jsonify({
            'labels': list(categories.keys()),
            'data': list(categories.values())
        }), 200
    except Exception as e:
        return jsonify({'error': 'Database error', 'details': str(e)}), 500

# CSV Export Endpoint for Duration Distribution
@app.route('/api/export/duration-distribution-csv')
def export_duration_distribution_csv():
    try:
        duration_data = get_duration_distribution().get_json()
        csv_data = generate_chart_csv(
            duration_data['labels'], 
            duration_data['data'],
            'Duration Category'
        )
        
        filename = f"duration_distribution_{get_ist_now().strftime('%Y%m%d_%H%M%S')}_IST.csv"
        
        return Response(
            csv_data,
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename={filename}'}
        )
        
    except Exception as e:
        return jsonify({'error': 'Export failed', 'details': str(e)}), 500

if __name__ == '__main__':
    print("🚀 Starting Machine Monitoring Flask App...")
    print("🌐 Server will be available at:")
    print("   - http://localhost:5000")
    print("   - http://127.0.0.1:5000")
    print("   - http://0.0.0.0:5000")
    # print("📱 Login credentials:")
    # print("   - Admin: pankaj / pankaj123")
    # print("   - User1: user1 / password1")
    # print("   - User2: user2 / password2")
    # print("   - User3: user3 / password3")
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 50)

    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Error starting Flask app: {e}")
        print("💡 Make sure port 5000 is not already in use")
        print("💡 Try running: netstat -ano | findstr :5000")