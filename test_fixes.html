<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fixes</title>
</head>
<body>
    <h1>Testing JavaScript and Static File Fixes</h1>
    
    <div id="test-results">
        <h2>Test Results:</h2>
        <ul id="results-list"></ul>
    </div>
    
    <script>
        const results = document.getElementById('results-list');
        
        // Test 1: Check if JavaScript syntax is valid
        try {
            eval('function testFunction() { return "test"; }');
            results.innerHTML += '<li style="color: green;">✅ JavaScript syntax test passed</li>';
        } catch (e) {
            results.innerHTML += '<li style="color: red;">❌ JavaScript syntax error: ' + e.message + '</li>';
        }
        
        // Test 2: Check if fetch API works
        try {
            fetch('/favicon.ico')
                .then(response => {
                    if (response.status === 204) {
                        results.innerHTML += '<li style="color: green;">✅ Favicon route working (204 No Content)</li>';
                    } else {
                        results.innerHTML += '<li style="color: orange;">⚠️ Favicon route returns: ' + response.status + '</li>';
                    }
                })
                .catch(error => {
                    results.innerHTML += '<li style="color: red;">❌ Favicon test failed: ' + error.message + '</li>';
                });
        } catch (e) {
            results.innerHTML += '<li style="color: red;">❌ Fetch API error: ' + e.message + '</li>';
        }
        
        // Test 3: Check if static CSS loads
        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = '/static/css/style.css';
        cssLink.onload = function() {
            results.innerHTML += '<li style="color: green;">✅ CSS file loads successfully</li>';
        };
        cssLink.onerror = function() {
            results.innerHTML += '<li style="color: red;">❌ CSS file failed to load</li>';
        };
        document.head.appendChild(cssLink);
        
        // Test 4: Check if static JS loads
        const jsScript = document.createElement('script');
        jsScript.src = '/static/js/script.js';
        jsScript.onload = function() {
            results.innerHTML += '<li style="color: green;">✅ JavaScript file loads successfully</li>';
        };
        jsScript.onerror = function() {
            results.innerHTML += '<li style="color: red;">❌ JavaScript file failed to load</li>';
        };
        document.head.appendChild(jsScript);
        
        results.innerHTML += '<li style="color: blue;">ℹ️ Test completed. Check above for results.</li>';
    </script>
</body>
</html>
