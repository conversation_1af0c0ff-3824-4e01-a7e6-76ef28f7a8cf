{% extends "index.html" %}

{% block content %}
<!-- Dashboard Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="dashboard-title mb-0">
                <i class="bi bi-speedometer2 me-2"></i>Dashboard
            </h1>
            <div class="badge bg-success fs-6 px-3 py-2">
                <i class="bi bi-circle-fill me-1" style="font-size: 0.5rem;"></i>
                Live Monitoring
            </div>
        </div>
    </div>
</div>

<!-- Status Cards Row -->
<div class="row mb-4 g-3">
    <div class="col-lg-4 col-md-6">
        <div class="card status-card h-100 border-0 shadow-sm" id="statusCard" data-loading="dashboard">
            <div class="card-body d-flex flex-column justify-content-center text-center p-4">
                <div class="status-icon mb-3">
                    <i class="bi bi-power fs-1 text-muted"></i>
                </div>
                <h5 class="card-title text-muted mb-2">Current Status</h5>
                <h2 class="display-3 fw-bold mb-0" id="currentStatus">--</h2>
                <small class="text-muted mt-2" id="statusTime">Checking...</small>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6">
        <div class="card metric-card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);" data-loading="dashboard">
            <div class="card-body d-flex flex-column justify-content-center text-center p-4 text-white">
                <div class="metric-icon mb-3">
                    <i class="bi bi-clock-history fs-1"></i>
                </div>
                <h5 class="card-title mb-2 opacity-90">Total Runtime</h5>
                <h2 class="display-4 fw-bold mb-0" id="totalRuntime">00:00:00</h2>
                <small class="opacity-75 mt-2">Hours : Minutes : Seconds</small>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6">
        <div class="card metric-card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);" data-loading="dashboard">
            <div class="card-body d-flex flex-column justify-content-center text-center p-4 text-white">
                <div class="metric-icon mb-3">
                    <i class="bi bi-graph-up fs-1"></i>
                </div>
                <h5 class="card-title mb-2 opacity-90">Total Events</h5>
                <h2 class="display-4 fw-bold mb-0" id="totalEvents">0</h2>
                <small class="opacity-75 mt-2">Machine Operations</small>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row mb-4 g-3">
    <div class="col-lg-8">
        <div class="card chart-card h-100 border-0 shadow-sm" data-loading="dashboard">
            <div class="card-header border-0" style="background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);">
                <div class="d-flex align-items-center">
                    <i class="bi bi-bar-chart-line me-2"></i>
                    <h5 class="mb-0 text-white">Daily Runtime Trend</h5>
                    <span class="badge bg-light text-dark ms-auto">Last 7 Days</span>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="chart-container">
                    <canvas id="dailyRuntimeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card chart-card h-100 border-0 shadow-sm" data-loading="dashboard">
            <div class="card-header border-0" style="background: linear-gradient(135deg, #8e44ad 0%, #3498db 100%);">
                <div class="d-flex align-items-center">
                    <i class="bi bi-pie-chart me-2"></i>
                    <h5 class="mb-0 text-white">Duration Distribution</h5>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="chart-container">
                    <canvas id="durationChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4 g-3">
    <div class="col-12">
        <div class="card chart-card border-0 shadow-sm" data-loading="dashboard">
            <div class="card-header border-0" style="background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);">
                <div class="d-flex align-items-center">
                    <i class="bi bi-graph-up-arrow me-2"></i>
                    <h5 class="mb-0 text-white">Hourly Activity Pattern</h5>
                    <span class="badge bg-light text-dark ms-auto">Last 30 Days</span>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="chart-container">
                    <canvas id="hourlyChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Events Table -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header border-0" style="background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);">
                <div class="d-flex align-items-center">
                    <i class="bi bi-clock-history me-2"></i>
                    <h5 class="mb-0 text-white">Recent Events</h5>
                    <a href="/api/export/events-csv" class="btn btn-sm btn-light ms-auto">
                        <i class="bi bi-download me-1"></i> Export
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Start Time</th>
                                <th>End Time</th>
                                <th>Duration</th>
                            </tr>
                        </thead>
                        <tbody id="recentEventsBody">
                            <tr>
                                <td colspan="3" class="text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Global chart references
    let dailyRuntimeChart, hourlyChart, durationChart;
    
    // Format duration in seconds to HH:MM:SS
    function formatDuration(seconds) {
        const totalSeconds = parseInt(seconds) || 0;
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const secs = totalSeconds % 60;
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    // Update status cards with summary data
    function updateSummaryCards(data) {
        // Update total events
        document.getElementById('totalEvents').textContent = data.total_events || 0;
        
        // Update total runtime
        document.getElementById('totalRuntime').textContent = formatDuration(data.total_runtime || 0);
        
        // Update status indicator
        const statusCard = document.getElementById('statusCard');
        const currentStatus = document.getElementById('currentStatus');
        const statusTime = document.getElementById('statusTime');
        
        if (data.last_event) {
            const lastEventEnd = new Date(data.last_event.end_time);
            const minutesAgo = Math.floor((new Date() - lastEventEnd) / 60000);
            
            if (minutesAgo < 2) {
                currentStatus.textContent = 'ON';
                statusCard.classList.add('status-on');
                statusCard.classList.remove('status-off');
                statusTime.textContent = 'Currently Running';
            } else {
                currentStatus.textContent = 'OFF';
                statusCard.classList.add('status-off');
                statusCard.classList.remove('status-on');
                if (minutesAgo < 60) {
                    statusTime.textContent = `Last seen ${minutesAgo} minutes ago`;
                } else {
                    const hoursAgo = Math.floor(minutesAgo / 60);
                    statusTime.textContent = `Last seen ${hoursAgo} hours ago`;
                }
            }
        } else {
            currentStatus.textContent = 'N/A';
            statusCard.classList.remove('status-on', 'status-off');
            statusTime.textContent = 'No data available';
        }
    }
    
    // Update recent events table
    function updateRecentEventsTable(events) {
        const tableBody = document.getElementById('recentEventsBody');
        
        if (!events || !Array.isArray(events) || events.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="3" class="text-center py-5 text-muted">
                        <i class="bi bi-inbox fs-1"></i>
                        <p class="mt-2 mb-0">No recent events</p>
                    </td>
                </tr>
            `;
            return;
        }
        
        // Sort by start_time (newest first) and take top 5
        const recentEvents = [...events]
            .sort((a, b) => new Date(b.start_time) - new Date(a.start_time))
            .slice(0, 5);
        
        tableBody.innerHTML = '';
        
        recentEvents.forEach((event, index) => {
            const row = document.createElement('tr');
            row.className = 'table-row-fade-in';
            row.style.animationDelay = `${index * 0.1}s`;
            
            row.innerHTML = `
                <td>${new Date(event.start_time).toLocaleString()}</td>
                <td>${new Date(event.end_time).toLocaleString()}</td>
                <td>
                    <span class="badge bg-primary">
                        ${formatDuration(event.duration)}
                    </span>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
    }
    
    // Create/update daily runtime chart
    function updateDailyRuntimeChart(data) {
        const ctx = document.getElementById('dailyRuntimeChart').getContext('2d');
        
        // Destroy existing chart
        if (dailyRuntimeChart) {
            dailyRuntimeChart.destroy();
        }
        
        dailyRuntimeChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels || [],
                datasets: [{
                    label: 'Runtime (hours)',
                    data: data.data || [],
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderWidth: 1
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { 
                            display: true, 
                            text: 'Hours', 
                            color: '#666',
                            font: { size: 12, weight: 'bold' }
                        },
                        grid: { 
                            color: 'rgba(0,0,0,0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#666',
                            padding: 8
                        }
                    },
                    x: {
                        grid: { 
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            color: '#666',
                            padding: 8
                        }
                    }
                }
            }
        });
    }
    
    // Create/update hourly distribution chart
    function updateHourlyChart(data) {
        const ctx = document.getElementById('hourlyChart').getContext('2d');
        
        // Destroy existing chart
        if (hourlyChart) {
            hourlyChart.destroy();
        }
        
        hourlyChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels || [],
                datasets: [{
                    label: 'Total Runtime (hours)',
                    data: data.data || [],
                    backgroundColor: 'rgba(255, 99, 132, 0.6)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1,
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderWidth: 1
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { 
                            display: true, 
                            text: 'Hours', 
                            color: '#666',
                            font: { size: 12, weight: 'bold' }
                        },
                        grid: { 
                            color: 'rgba(0,0,0,0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#666',
                            padding: 8
                        }
                    },
                    x: {
                        title: { 
                            display: true, 
                            text: 'Hour of Day', 
                            color: '#666',
                            font: { size: 12, weight: 'bold' }
                        },
                        grid: { 
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            color: '#666',
                            padding: 8
                        }
                    }
                }
            }
        });
    }
    
    // Create/update duration distribution chart
    function updateDurationChart(data) {
        const ctx = document.getElementById('durationChart').getContext('2d');
        
        // Destroy existing chart
        if (durationChart) {
            durationChart.destroy();
        }
        
        durationChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels || [],
                datasets: [{
                    data: data.data || [],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 205, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(153, 102, 255, 0.8)',
                        'rgba(255, 159, 64, 0.8)'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: { size: 11 }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderWidth: 1
                    }
                },
                cutout: '60%'
            }
        });
    }
    
    // Load all dashboard data
    async function loadDashboardData() {
        try {
            // Show loading states
            document.querySelectorAll('[data-loading="dashboard"]').forEach(el => {
                el.classList.add('card-loading');
            });
            
            // Fetch all data in parallel
            const [summaryRes, eventsRes, dailyRes, hourlyRes, durationRes] = await Promise.all([
                fetch('/api/summary'),
                fetch('/api/machine-data'),
                fetch('/api/charts/daily-runtime'),
                fetch('/api/charts/hourly-distribution'),
                fetch('/api/charts/duration-distribution')
            ]);
            
            // Handle responses
            if (!summaryRes.ok) throw new Error('Failed to load summary');
            if (!eventsRes.ok) throw new Error('Failed to load events');
            if (!dailyRes.ok) throw new Error('Failed to load daily data');
            if (!hourlyRes.ok) throw new Error('Failed to load hourly data');
            if (!durationRes.ok) throw new Error('Failed to load duration data');
            
            const summary = await summaryRes.json();
            const events = await eventsRes.json();
            const dailyData = await dailyRes.json();
            const hourlyData = await hourlyRes.json();
            const durationData = await durationRes.json();
            
            // Update UI
            updateSummaryCards(summary);
            updateRecentEventsTable(events);
            updateDailyRuntimeChart(dailyData);
            updateHourlyChart(hourlyData);
            updateDurationChart(durationData);
            
            // Update last updated time
            document.getElementById('lastUpdate').textContent = 
                `Last updated: ${new Date().toLocaleTimeString()}`;
                
        } catch (error) {
            console.error('Dashboard load error:', error);
            
            // Show error notification
            const notification = document.createElement('div');
            notification.className = 'alert alert-danger position-fixed top-0 end-0 m-3';
            notification.style.zIndex = '1060';
            notification.innerHTML = `
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                ${error.message}
            `;
            document.body.appendChild(notification);
            
            // Auto-remove notification
            setTimeout(() => {
                notification.remove();
            }, 5000);
            
        } finally {
            // Hide loading states
            document.querySelectorAll('[data-loading="dashboard"]').forEach(el => {
                el.classList.remove('card-loading');
            });
        }
    }
    
    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', () => {
        // Initial load
        loadDashboardData();
        
        // Auto-refresh every 30 seconds
        setInterval(loadDashboardData, 30000);
        
        // Add last update indicator
        const header = document.querySelector('.dashboard-title').parentElement;
        const updateEl = document.createElement('div');
        updateEl.id = 'lastUpdate';
        updateEl.className = 'text-muted small mt-2';
        updateEl.textContent = 'Loading...';
        header.appendChild(updateEl);
    });
</script>
{% endblock %}