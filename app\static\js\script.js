/**
 * Machine Monitoring System - Perfect JavaScript Implementation
 * Optimized for Flask backend with Supabase integration
 */

class MachineMonitoringApp {
    constructor() {
        // Configuration
        this.config = {
            refreshInterval: 30000, // 30 seconds
            chartRefreshInterval: 60000, // 1 minute
            eventsPerPage: 10,
            maxRetries: 3,
            retryDelay: 1000
        };

        // State management
        this.state = {
            currentPage: 1,
            totalEvents: 0,
            isLoading: false,
            lastUpdate: null,
            charts: {},
            intervals: [],
            retryCount: 0
        };

        // Initialize the application
        this.init();
    }

    /**
     * Initialize the application based on current page
     */
    init() {
        console.log('🚀 Initializing Machine Monitoring System...');

        // Detect current page and initialize accordingly
        if (this.isDashboardPage()) {
            this.initDashboard();
        } else if (this.isEventsPage()) {
            this.initEventsPage();
        } else if (this.isTestPage()) {
            this.initTestPage();
        }

        // Global event listeners
        this.setupGlobalEventListeners();

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => this.cleanup());

        console.log('✅ Machine Monitoring System initialized successfully');
    }

    /**
     * Page detection methods
     */
    isDashboardPage() {
        return document.querySelector('.dashboard') !== null ||
               document.getElementById('dailyRuntimeChart') !== null;
    }

    isEventsPage() {
        return document.querySelector('.events') !== null ||
               document.getElementById('eventsTable') !== null;
    }

    isTestPage() {
        return document.querySelector('[href="/test"]') !== null ||
               window.location.pathname === '/test';
    }
    /**
     * Dashboard initialization
     */
    initDashboard() {
        console.log('📊 Initializing Dashboard...');

        // Load initial data
        this.loadDashboardData();

        // Set up auto-refresh
        const dashboardInterval = setInterval(() => {
            if (!this.state.isLoading) {
                this.loadDashboardData();
            }
        }, this.config.refreshInterval);

        this.state.intervals.push(dashboardInterval);
    }

    /**
     * Events page initialization
     */
    initEventsPage() {
        console.log('📋 Initializing Events Page...');

        // Load initial events
        this.loadEventsData();

        // Set up filter handlers
        this.setupEventFilters();

        // Set up auto-refresh
        const eventsInterval = setInterval(() => {
            if (!this.state.isLoading) {
                this.loadEventsData();
            }
        }, this.config.refreshInterval);

        this.state.intervals.push(eventsInterval);
    }

    /**
     * Test page initialization
     */
    initTestPage() {
        console.log('🧪 Initializing Test Page...');
        this.setupTestPageHandlers();
    }

    /**
     * Global event listeners setup
     */
    setupGlobalEventListeners() {
        // Handle network status changes
        window.addEventListener('online', () => {
            this.showNotification('Connection restored', 'success');
            this.retryFailedRequests();
        });

        window.addEventListener('offline', () => {
            this.showNotification('Connection lost', 'warning');
        });

        // Handle visibility changes (pause updates when tab is hidden)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseUpdates();
            } else {
                this.resumeUpdates();
            }
        });
    }
    
    /**
     * Load dashboard data (summary, recent events, charts)
     */
    async loadDashboardData() {
        if (this.state.isLoading) return;

        this.state.isLoading = true;
        this.showLoadingState('dashboard');

        try {
            // Load all dashboard data in parallel
            const [summaryData, eventsData, chartData] = await Promise.allSettled([
                this.fetchSummary(),
                this.fetchRecentEvents(),
                this.fetchChartData()
            ]);

            // Handle results
            if (summaryData.status === 'fulfilled') {
                this.updateSummaryCards(summaryData.value);
            }

            if (eventsData.status === 'fulfilled') {
                this.updateRecentEventsTable(eventsData.value);
            }

            if (chartData.status === 'fulfilled') {
                this.updateCharts(chartData.value);
            }

            this.state.lastUpdate = new Date();
            this.state.retryCount = 0;

        } catch (error) {
            this.handleError(error, 'Loading dashboard data');
        } finally {
            this.state.isLoading = false;
            this.hideLoadingState('dashboard');
        }
    }

    /**
     * Fetch summary data from API
     */
    async fetchSummary() {
        const response = await this.makeRequest('/api/summary');
        return response;
    }

    /**
     * Update summary cards with data
     */
    updateSummaryCards(data) {
        // Update total runtime
        const totalRuntimeEl = document.getElementById('total-runtime') ||
                              document.getElementById('todayRuntime');
        if (totalRuntimeEl) {
            totalRuntimeEl.textContent = this.formatDuration(data.total_runtime || 0);
        }

        // Update total events
        const totalEventsEl = document.getElementById('total-events') ||
                             document.getElementById('totalEvents');
        if (totalEventsEl) {
            totalEventsEl.textContent = data.total_events || 0;
        }

        // Update last event
        const lastEventEl = document.getElementById('last-event');
        if (lastEventEl) {
            if (data.last_event) {
                const lastEvent = data.last_event;
                lastEventEl.innerHTML = `
                    ${this.formatDateTime(lastEvent.start_time)}<br>
                    <span class="badge bg-info">${this.formatDuration(lastEvent.duration)}</span>
                `;
            } else {
                lastEventEl.innerHTML = '<span class="text-muted">No events recorded</span>';
            }
        }

        // Update status indicator if present
        this.updateStatusIndicator(data.last_event);
    }
    
    /**
     * Fetch recent events from API
     */
    async fetchRecentEvents() {
        const response = await this.makeRequest('/api/machine-data');
        return response;
    }

    /**
     * Update recent events table
     */
    updateRecentEventsTable(events) {
        const tableBody = document.querySelector('#recent-events tbody') ||
                         document.querySelector('#eventsTableBody');

        if (!tableBody) {
            // Table not found on this page - this is normal for non-dashboard pages
            return;
        }

        if (!Array.isArray(events) || events.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center text-muted py-4">
                        <i class="bi bi-inbox fs-1"></i>
                        <p class="mt-2 mb-0">No recent events</p>
                    </td>
                </tr>
            `;
            return;
        }

        // Sort events by start_time (newest first) and take first 5
        const recentEvents = events
            .sort((a, b) => new Date(b.start_time) - new Date(a.start_time))
            .slice(0, 5);

        tableBody.innerHTML = '';

        recentEvents.forEach((event, index) => {
            const row = document.createElement('tr');
            row.className = 'table-row-fade-in';
            row.style.animationDelay = `${index * 0.1}s`;

            // Different row structure for different tables
            if (document.querySelector('#eventsTable')) {
                // Events page table (with ID column)
                row.innerHTML = `
                    <td><span class="badge bg-secondary">${event.id || 'N/A'}</span></td>
                    <td>
                        <div class="fw-medium">${this.formatDate(event.start_time)}</div>
                        <small class="text-muted">${this.formatTime(event.start_time)}</small>
                    </td>
                    <td>
                        <div class="fw-medium">${this.formatDate(event.end_time)}</div>
                        <small class="text-muted">${this.formatTime(event.end_time)}</small>
                    </td>
                    <td><span class="badge bg-info">${this.formatDuration(event.duration)}</span></td>
                    <td><span class="badge bg-success">ON</span></td>
                `;
            } else {
                // Dashboard recent events table
                row.innerHTML = `
                    <td>${this.formatDateTime(event.start_time)}</td>
                    <td>${this.formatDateTime(event.end_time)}</td>
                    <td><span class="badge bg-primary">${this.formatDuration(event.duration)}</span></td>
                `;
            }

            tableBody.appendChild(row);
        });
    }
    
    /**
     * Fetch chart data from multiple endpoints
     */
    async fetchChartData() {
        const [dailyRuntime, hourlyDistribution, durationDistribution] = await Promise.allSettled([
            this.makeRequest('/api/charts/daily-runtime'),
            this.makeRequest('/api/charts/hourly-distribution'),
            this.makeRequest('/api/charts/duration-distribution')
        ]);

        return {
            dailyRuntime: dailyRuntime.status === 'fulfilled' ? dailyRuntime.value : null,
            hourlyDistribution: hourlyDistribution.status === 'fulfilled' ? hourlyDistribution.value : null,
            durationDistribution: durationDistribution.status === 'fulfilled' ? durationDistribution.value : null
        };
    }

    /**
     * Update all charts with new data
     */
    updateCharts(chartData) {
        if (chartData.dailyRuntime) {
            this.updateDailyRuntimeChart(chartData.dailyRuntime);
        }

        if (chartData.hourlyDistribution) {
            this.updateHourlyChart(chartData.hourlyDistribution);
        }

        if (chartData.durationDistribution) {
            this.updateDurationChart(chartData.durationDistribution);
        }
    }

    /**
     * Update daily runtime chart
     */
    updateDailyRuntimeChart(data) {
        const canvas = document.getElementById('dailyRuntimeChart');
        if (!canvas) return;

        // Destroy existing chart completely
        if (this.state.charts.dailyRuntime) {
            try {
                this.state.charts.dailyRuntime.destroy();
            } catch (e) {
                console.warn('Error destroying daily runtime chart:', e);
            }
            this.state.charts.dailyRuntime = null;
        }

        // Clear any existing Chart.js instances on this canvas
        const existingChart = Chart.getChart(canvas);
        if (existingChart) {
            existingChart.destroy();
        }

        const ctx = canvas.getContext('2d');
        this.state.charts.dailyRuntime = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels || [],
                datasets: [{
                    label: 'Runtime (hours)',
                    data: data.data || [],
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4
                }]
            },
            options: this.getChartOptions('line')
        });
    }
    
    /**
     * Update hourly distribution chart
     */
    updateHourlyChart(data) {
        const canvas = document.getElementById('hourlyChart');
        if (!canvas) return;

        // Destroy existing chart completely
        if (this.state.charts.hourly) {
            try {
                this.state.charts.hourly.destroy();
            } catch (e) {
                console.warn('Error destroying hourly chart:', e);
            }
            this.state.charts.hourly = null;
        }

        // Clear any existing Chart.js instances on this canvas
        const existingChart = Chart.getChart(canvas);
        if (existingChart) {
            existingChart.destroy();
        }

        const ctx = canvas.getContext('2d');
        this.state.charts.hourly = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels || [],
                datasets: [{
                    label: 'Total Runtime (hours)',
                    data: data.data || [],
                    backgroundColor: 'rgba(255, 99, 132, 0.6)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1,
                    borderRadius: 4
                }]
            },
            options: this.getChartOptions('bar')
        });
    }

    /**
     * Update duration distribution chart
     */
    updateDurationChart(data) {
        const canvas = document.getElementById('durationChart');
        if (!canvas) return;

        // Destroy existing chart completely
        if (this.state.charts.duration) {
            try {
                this.state.charts.duration.destroy();
            } catch (e) {
                console.warn('Error destroying duration chart:', e);
            }
            this.state.charts.duration = null;
        }

        // Clear any existing Chart.js instances on this canvas
        const existingChart = Chart.getChart(canvas);
        if (existingChart) {
            existingChart.destroy();
        }

        const ctx = canvas.getContext('2d');
        this.state.charts.duration = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels || [],
                datasets: [{
                    data: data.data || [],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 205, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(153, 102, 255, 0.8)',
                        'rgba(255, 159, 64, 0.8)'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: this.getChartOptions('doughnut')
        });
    }

    /**
     * Get chart options based on chart type
     */
    getChartOptions(type) {
        const baseOptions = {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            },
            plugins: {
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderWidth: 1
                }
            }
        };

        switch (type) {
            case 'line':
                return {
                    ...baseOptions,
                    plugins: {
                        ...baseOptions.plugins,
                        legend: { display: false }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: 'Hours', color: '#666' },
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        x: {
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        }
                    }
                };
            case 'bar':
                return {
                    ...baseOptions,
                    plugins: {
                        ...baseOptions.plugins,
                        legend: { display: false }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: 'Hours', color: '#666' },
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        x: {
                            title: { display: true, text: 'Hour of Day', color: '#666' },
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        }
                    }
                };
            case 'doughnut':
                return {
                    ...baseOptions,
                    plugins: {
                        ...baseOptions.plugins,
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: { size: 12 }
                            }
                        }
                    }
                };
            default:
                return baseOptions;
        }
    }
    
    /**
     * Load events data for events page
     */
    async loadEventsData() {
        if (this.state.isLoading) return;

        this.state.isLoading = true;
        this.showLoadingState('events');

        try {
            const events = await this.fetchAllEvents();
            this.updateEventsPage(events);
            this.state.retryCount = 0;
        } catch (error) {
            this.handleError(error, 'Loading events data');
        } finally {
            this.state.isLoading = false;
            this.hideLoadingState('events');
        }
    }

    /**
     * Fetch all events with filters
     */
    async fetchAllEvents() {
        const dateFilter = document.getElementById('date-filter');
        const durationFilter = document.getElementById('duration-filter');

        let url = `/api/machine-data?page=${this.state.currentPage}&per_page=${this.config.eventsPerPage}`;

        // Add filters if provided and elements exist
        if (dateFilter && dateFilter.value) {
            url += `&date=${dateFilter.value}`;
        }
        if (durationFilter && durationFilter.value) {
            url += `&min_duration=${durationFilter.value * 60}`; // Convert minutes to seconds
        }

        const response = await this.makeRequest(url);
        return response;
    }

    /**
     * Update events page with new data
     */
    updateEventsPage(events) {
        // Update summary cards
        this.updateEventsSummaryCards(events);

        // Update events table
        this.updateEventsTable(events);

        // Update pagination
        this.updatePagination(events);

        // Update total events count
        this.state.totalEvents = Array.isArray(events) ? events.length : 0;
    }

    /**
     * Update events page summary cards
     */
    updateEventsSummaryCards(events) {
        if (!Array.isArray(events)) return;

        const today = new Date().toDateString();
        const todayEvents = events.filter(event =>
            new Date(event.start_time).toDateString() === today
        );

        // Total events count
        const totalEventsEl = document.getElementById('totalEventsCount');
        if (totalEventsEl) {
            totalEventsEl.textContent = events.length;
        }

        // Today's events count
        const todayEventsEl = document.getElementById('todayEventsCount');
        if (todayEventsEl) {
            todayEventsEl.textContent = todayEvents.length;
        }

        // Today's total runtime
        const todayRuntimeEl = document.getElementById('todayTotalRuntime');
        if (todayRuntimeEl) {
            const todayRuntime = todayEvents.reduce((total, event) => total + (event.duration || 0), 0);
            todayRuntimeEl.textContent = this.formatDuration(todayRuntime);
        }

        // Average duration
        const avgDurationEl = document.getElementById('avgDuration');
        if (avgDurationEl && events.length > 0) {
            const avgDuration = events.reduce((total, event) => total + (event.duration || 0), 0) / events.length;
            avgDurationEl.textContent = `${Math.round(avgDuration / 60)} min`;
        }
    }
    
    /**
     * Update events table
     */
    updateEventsTable(events) {
        const tableBody = document.querySelector('#all-events tbody') ||
                         document.querySelector('#eventsTableBody');

        if (!tableBody) {
            console.warn('Events table not found on this page');
            return;
        }

        if (!Array.isArray(events) || events.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center text-muted p-4">
                        <i class="bi bi-inbox fs-1"></i>
                        <p class="mt-2 mb-0">No events found</p>
                        <p class="small">Add some test data to see events here</p>
                    </td>
                </tr>
            `;
            return;
        }

        // Sort events by start_time (newest first)
        const sortedEvents = [...events].sort((a, b) => new Date(b.start_time) - new Date(a.start_time));

        tableBody.innerHTML = '';

        sortedEvents.forEach((event, index) => {
            const row = document.createElement('tr');
            row.className = 'table-row-fade-in';
            row.style.animationDelay = `${index * 0.1}s`;

            row.innerHTML = `
                <td><span class="badge bg-secondary">${event.id || 'N/A'}</span></td>
                <td>
                    <div class="fw-medium">${this.formatDate(event.start_time)}</div>
                    <small class="text-muted">${this.formatTime(event.start_time)}</small>
                </td>
                <td>
                    <div class="fw-medium">${this.formatDate(event.end_time)}</div>
                    <small class="text-muted">${this.formatTime(event.end_time)}</small>
                </td>
                <td><span class="badge bg-info">${this.formatDuration(event.duration)}</span></td>
                <td><span class="badge bg-success">ON</span></td>
            `;

            tableBody.appendChild(row);
        });
    }

    /**
     * Render pagination controls
     */
    renderPagination() {
        const pagination = document.getElementById('pagination');
        if (!pagination) return;

        pagination.innerHTML = '';

        // Use default values if state properties don't exist
        const totalEvents = this.state?.totalEvents || 0;
        const eventsPerPage = this.state?.eventsPerPage || 10;
        const currentPage = this.state?.currentPage || 1;

        const totalPages = Math.ceil(totalEvents / eventsPerPage);
        if (totalPages <= 1) return; // No pagination needed

        // Previous button
        const prevItem = document.createElement('li');
        prevItem.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevItem.innerHTML = `
            <a class="page-link" href="#" aria-label="Previous" ${currentPage === 1 ? 'tabindex="-1"' : ''}>
                <span aria-hidden="true">&laquo;</span>
            </a>
        `;
        const self = this;
        prevItem.querySelector('a').addEventListener('click', function(e) {
            e.preventDefault();
            if (currentPage > 1) {
                if (self.state) self.state.currentPage = currentPage - 1;
                self.loadEventsData();
            }
        });
        pagination.appendChild(prevItem);

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            const pageItem = document.createElement('li');
            pageItem.className = `page-item ${i === currentPage ? 'active' : ''}`;
            pageItem.innerHTML = `<a class="page-link" href="#">${i}</a>`;
            pageItem.querySelector('a').addEventListener('click', function(e) {
                e.preventDefault();
                if (self.state) self.state.currentPage = i;
                self.loadEventsData();
            });
            pagination.appendChild(pageItem);
        }

        // Next button
        const nextItem = document.createElement('li');
        nextItem.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextItem.innerHTML = `
            <a class="page-link" href="#" aria-label="Next" ${currentPage === totalPages ? 'tabindex="-1"' : ''}>
                <span aria-hidden="true">&raquo;</span>
            </a>
        `;
        nextItem.querySelector('a').addEventListener('click', function(e) {
            e.preventDefault();
            if (currentPage < totalPages) {
                if (self.state) self.state.currentPage = currentPage + 1;
                self.loadEventsData();
            }
        });
        pagination.appendChild(nextItem);
    }
    
    /**
     * Core utility methods
     */

    /**
     * Make HTTP request with retry logic
     */
    async makeRequest(url, options = {}) {
        const maxRetries = this.config.maxRetries;
        let lastError;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                if (data.error) {
                    throw new Error(data.error);
                }

                return data;
            } catch (error) {
                lastError = error;
                console.warn(`Request attempt ${attempt} failed:`, error.message);

                if (attempt < maxRetries) {
                    await this.delay(this.config.retryDelay * attempt);
                }
            }
        }

        throw lastError;
    }

    /**
     * Delay utility
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Format date and time in IST
     */
    formatDateTime(datetimeStr) {
        try {
            const date = new Date(datetimeStr);
            if (isNaN(date.getTime())) {
                return 'Invalid Date';
            }

            // Convert to IST (UTC+5:30)
            const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
            const istDate = new Date(date.getTime() + istOffset);

            return istDate.toLocaleString('en-IN', {
                timeZone: 'Asia/Kolkata',
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }) + ' IST';
        } catch (error) {
            console.error('Error formatting date:', error);
            return 'Invalid Date';
        }
    }

    /**
     * Format date only
     */
    formatDate(datetimeStr) {
        try {
            const date = new Date(datetimeStr);
            if (isNaN(date.getTime())) {
                return 'Invalid Date';
            }
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch (error) {
            console.error('Error formatting date:', error);
            return 'Invalid Date';
        }
    }

    /**
     * Format time only
     */
    formatTime(datetimeStr) {
        try {
            const date = new Date(datetimeStr);
            if (isNaN(date.getTime())) {
                return 'Invalid Time';
            }
            return date.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            console.error('Error formatting time:', error);
            return 'Invalid Time';
        }
    }

    /**
     * Format duration in seconds to human readable format
     */
    formatDuration(seconds) {
        try {
            const totalSeconds = parseInt(seconds) || 0;
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const secs = totalSeconds % 60;

            if (hours > 0) {
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else if (minutes > 0) {
                return `${minutes}m ${secs}s`;
            } else {
                return `${secs}s`;
            }
        } catch (error) {
            console.error('Error formatting duration:', error);
            return '0s';
        }
    }

    /**
     * Show notification to user
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;';
        notification.innerHTML = `
            <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    /**
     * Handle errors with user feedback
     */
    handleError(error, context = 'Operation') {
        console.error(`${context} failed:`, error);
        this.showNotification(`${context} failed: ${error.message}`, 'error');

        // Increment retry count
        this.state.retryCount++;

        // If too many retries, suggest page refresh
        if (this.state.retryCount >= this.config.maxRetries) {
            setTimeout(() => {
                this.showNotification('Multiple failures detected. Consider refreshing the page.', 'warning');
            }, 2000);
        }
    }

    /**
     * Show loading state
     */
    showLoadingState(context) {
        const loadingElements = document.querySelectorAll(`[data-loading="${context}"]`);
        loadingElements.forEach(el => {
            el.classList.add('card-loading');
        });
    }

    /**
     * Hide loading state
     */
    hideLoadingState(context) {
        const loadingElements = document.querySelectorAll(`[data-loading="${context}"]`);
        loadingElements.forEach(el => {
            el.classList.remove('card-loading');
        });
    }

    /**
     * Update status indicator based on last event
     */
    updateStatusIndicator(lastEvent) {
        const statusCard = document.getElementById('statusCard');
        const currentStatus = document.getElementById('currentStatus');
        const statusTime = document.getElementById('statusTime');

        if (!statusCard || !currentStatus) return;

        if (lastEvent) {
            const lastEventEnd = new Date(lastEvent.end_time);
            const minutesAgo = Math.floor((new Date() - lastEventEnd) / 60000);

            if (minutesAgo < 2) {
                currentStatus.textContent = 'ON';
                statusCard.classList.add('status-on');
                statusCard.classList.remove('status-off');
                if (statusTime) statusTime.textContent = 'Currently Running';
            } else {
                currentStatus.textContent = 'OFF';
                statusCard.classList.add('status-off');
                statusCard.classList.remove('status-on');
                if (statusTime) {
                    if (minutesAgo < 60) {
                        statusTime.textContent = `Last seen ${minutesAgo} minutes ago`;
                    } else {
                        const hoursAgo = Math.floor(minutesAgo / 60);
                        statusTime.textContent = `Last seen ${hoursAgo} hours ago`;
                    }
                }
            }
        } else {
            currentStatus.textContent = 'N/A';
            statusCard.classList.remove('status-on', 'status-off');
            if (statusTime) statusTime.textContent = 'No data available';
        }
    }

    /**
     * Setup event filters for events page
     */
    setupEventFilters() {
        const filterBtn = document.getElementById('apply-filters');
        const refreshBtn = document.getElementById('refreshBtn');

        if (filterBtn) {
            filterBtn.addEventListener('click', () => {
                this.state.currentPage = 1; // Reset to first page
                this.loadEventsData();
            });
        }

        if (refreshBtn) {
            refreshBtn.addEventListener('click', (e) => {
                const btn = e.target;
                const originalHTML = btn.innerHTML;
                btn.innerHTML = '<i class="bi bi-arrow-clockwise spin me-1"></i>Refreshing...';
                btn.disabled = true;

                this.loadEventsData().finally(() => {
                    btn.innerHTML = originalHTML;
                    btn.disabled = false;
                });
            });
        }
    }

    /**
     * Setup test page handlers
     */
    setupTestPageHandlers() {
        // This would be implemented based on your test page requirements
        console.log('Test page handlers setup complete');
    }

    /**
     * Update pagination
     */
    updatePagination(events) {
        const pagination = document.getElementById('pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.state.totalEvents / this.config.eventsPerPage);

        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // Previous button
        paginationHTML += `
            <li class="page-item ${this.state.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.state.currentPage - 1}">Previous</a>
            </li>
        `;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            paginationHTML += `
                <li class="page-item ${i === this.state.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        }

        // Next button
        paginationHTML += `
            <li class="page-item ${this.state.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.state.currentPage + 1}">Next</a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;

        // Add click handlers
        pagination.querySelectorAll('a.page-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = parseInt(e.target.dataset.page);
                if (page && page !== this.state.currentPage) {
                    this.state.currentPage = page;
                    this.loadEventsData();
                }
            });
        });
    }

    /**
     * Pause updates when tab is hidden
     */
    pauseUpdates() {
        this.state.intervals.forEach(interval => clearInterval(interval));
        this.state.intervals = [];
    }

    /**
     * Resume updates when tab becomes visible
     */
    resumeUpdates() {
        if (this.isDashboardPage()) {
            this.initDashboard();
        } else if (this.isEventsPage()) {
            this.initEventsPage();
        }
    }

    /**
     * Retry failed requests
     */
    retryFailedRequests() {
        this.state.retryCount = 0;

        if (this.isDashboardPage()) {
            this.loadDashboardData();
        } else if (this.isEventsPage()) {
            this.loadEventsData();
        }
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        // Clear all intervals
        this.state.intervals.forEach(interval => clearInterval(interval));

        // Destroy all charts
        Object.values(this.state.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });

        console.log('🧹 Machine Monitoring System cleaned up');
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Prevent multiple instances
    if (window.machineMonitoringApp) {
        console.log('Machine Monitoring App already initialized, skipping...');
        return;
    }

    window.machineMonitoringApp = new MachineMonitoringApp();

    // Expose useful methods globally for debugging
    window.machineMonitoring = {
        app: window.machineMonitoringApp,
        showNotification: (msg, type) => window.machineMonitoringApp.showNotification(msg, type),
        formatDateTime: (dt) => window.machineMonitoringApp.formatDateTime(dt),
        formatDuration: (dur) => window.machineMonitoringApp.formatDuration(dur),
        loadDashboard: () => window.machineMonitoringApp.loadDashboardData(),
        loadEvents: () => window.machineMonitoringApp.loadEventsData()
    };
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (window.machineMonitoringApp && typeof window.machineMonitoringApp.cleanup === 'function') {
        window.machineMonitoringApp.cleanup();
    }
});