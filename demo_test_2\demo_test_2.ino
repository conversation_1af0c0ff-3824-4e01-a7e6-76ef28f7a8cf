#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>  

// ========================================
// CONFIGURATION SECTION (FIXED)
// ========================================

// WiFi Configuration
const char* ssid = "PANKAJ PATIL";
const char* password = "pankaj@10";

// Server Configuration (FIXED ENDPOINT WITH CORRECT API KEY)
const char* serverURL = "http://103.181.200.14:5000/api/machine-data";
const char* apiKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2Zmx0a2JjaXdwcGF3Z2hxcGRsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MDI0NjYsImV4cCI6MjA2NjE3ODQ2Nn0.z_U2VOsbP1lMK88dDp7xmVTLkQQdO9hvI8s47JfpspE";

// Hardware Pin Configuration
#define LED_PIN 2  

// Simulation Configuration
#define SIMULATED_CURRENT_ON 15.0  
#define SIMULATED_CURRENT_OFF 0.5   
#define CURRENT_THRESHOLD 10.0      

// Timing Configuration
#define READING_INTERVAL 5000     
#define SEND_INTERVAL 30000       
#define STATE_CHANGE_INTERVAL 60000 

// ========================================
// GLOBAL VARIABLES (UPDATED)
// ========================================

// Machine State Variables
bool machineState = false;          
unsigned long lastStateChange = 0;  
unsigned long machineStartTime = 0; 
unsigned long currentEventStart = 0;

// Timing Variables
unsigned long lastReadingTime = 0;
unsigned long lastSendTime = 0;

// Current Monitoring Variables
float currentReading = 0.0;
float averageCurrent = 0.0;
float maxCurrent = 0.0;
int readingCount = 0;
float currentSum = 0.0;

// Network Status
bool wifiConnected = false;

// Event Tracking
bool eventPending = false;
unsigned long eventDuration = 0;

// ========================================
// SETUP FUNCTION
// ========================================

void setup() {
  Serial.begin(115200);
  Serial.println("========================================");
  Serial.println("Machine Monitoring System - FIXED VERSION");
  Serial.println("========================================");
  
  // Initialize LED
  pinMode(LED_PIN, OUTPUT);
  
  // Initial LED pattern
  blinkLED(3, 200);
  
  // Connect to WiFi
  connectToWiFi();
  
  Serial.println("🚀 System initialization complete!");
  Serial.println("📊 Starting machine monitoring...");
  
  // Ready indication
  blinkLED(5, 100);
  digitalWrite(LED_PIN, HIGH);
  
  // Initialize machine state
  machineState = false;
  lastStateChange = millis();
  currentEventStart = 0;
}

// ========================================
// MAIN LOOP (UPDATED)
// ========================================

void loop() {
  unsigned long currentTime = millis();

  // Generate readings
  if (currentTime - lastReadingTime >= READING_INTERVAL) {
    generateSimulatedReading();
    updateMachineState();
    lastReadingTime = currentTime;
  }

  // Send data to server
  if (currentTime - lastSendTime >= SEND_INTERVAL) {
    if (eventPending) {
      Serial.println("📤 Event pending - sending to server...");
      sendDataToServer();
    } else {
      Serial.printf("📊 Status: Machine %s, No completed events to send\n",
                   machineState ? "ON" : "OFF");
    }
    lastSendTime = currentTime;
  }

  // Check WiFi connection
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("❌ WiFi disconnected! Attempting to reconnect...");
    connectToWiFi();
  }

  // Update status LED
  updateStatusLED();

  delay(100);
}

// ========================================
// WIFI FUNCTIONS
// ========================================

void connectToWiFi() {
  Serial.println("📶 Connecting to WiFi...");
  WiFi.begin(ssid, password);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    digitalWrite(LED_PIN, !digitalRead(LED_PIN)); 
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    wifiConnected = true;
    Serial.println("\n✅ WiFi connected!");
    Serial.print("📍 IP address: ");
    Serial.println(WiFi.localIP());
    digitalWrite(LED_PIN, HIGH);
  } else {
    wifiConnected = false;
    Serial.println("\n❌ WiFi connection failed!");
    digitalWrite(LED_PIN, LOW);
  }
}

// ========================================
// DATA SIMULATION FUNCTIONS (UPDATED)
// ========================================

void generateSimulatedReading() {
  // Simulate machine ON/OFF cycles automatically for testing
  static unsigned long lastStateToggle = 0;
  static bool simulatedMachineOn = false;

  // Auto-toggle machine state every 45 seconds for testing
  if (millis() - lastStateToggle > 45000) {
    simulatedMachineOn = !simulatedMachineOn;
    lastStateToggle = millis();
    Serial.printf("🔄 Simulated machine state changed to: %s\n", simulatedMachineOn ? "ON" : "OFF");
  }

  // Generate current reading based on simulated machine state
  if (simulatedMachineOn) {
    currentReading = SIMULATED_CURRENT_ON + random(-200, 200) / 100.0;
  } else {
    currentReading = SIMULATED_CURRENT_OFF + random(-30, 30) / 100.0;
  }

  // Update statistics
  currentSum += currentReading;
  readingCount++;

  if (currentReading > maxCurrent) {
    maxCurrent = currentReading;
  }

  // Calculate average more frequently for better responsiveness
  if (readingCount >= 3) {
    averageCurrent = currentSum / readingCount;
    currentSum = 0;
    readingCount = 0;

    Serial.printf("⚡ Current: %.2fA (Avg: %.2fA, Max: %.2fA) [Simulated: %s]\n",
                  currentReading, averageCurrent, maxCurrent, simulatedMachineOn ? "ON" : "OFF");
  }
}

void updateMachineState() {
  // Detect state based on current threshold
  bool newState = (averageCurrent > CURRENT_THRESHOLD);

  if (newState != machineState) {
    machineState = newState;

    if (machineState) {
      // Machine turned ON
      machineStartTime = millis();
      currentEventStart = millis();
      Serial.println("🟢 MACHINE STATE DETECTED: ON");
      Serial.printf("   Start time: %lu ms\n", currentEventStart);
    } else {
      // Machine turned OFF
      if (currentEventStart > 0) {  // Only if we had a valid start time
        unsigned long duration = (millis() - currentEventStart) / 1000;
        Serial.printf("🔴 MACHINE STATE DETECTED: OFF (Duration: %lu seconds)\n", duration);
        Serial.printf("   Start: %lu ms, End: %lu ms\n", currentEventStart, millis());

        // Only create event if duration is reasonable (at least 5 seconds)
        if (duration >= 5) {
          eventDuration = duration;
          eventPending = true;
          Serial.println("   ✅ Event ready to send!");
        } else {
          Serial.println("   ⚠️ Duration too short, skipping event");
        }
      }
    }
  }
}

// ========================================
// DATA TRANSMISSION FUNCTIONS (FIXED)
// ========================================

void sendDataToServer() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("❌ WiFi not connected, cannot send data");
    return;
  }

  // Create JSON payload matching server requirements
  StaticJsonDocument<256> doc;

  // Generate timestamps for the event
  String startTime = getISOTimestamp(currentEventStart);
  String endTime = getISOTimestamp(millis());

  // Add required fields
  doc["start_time"] = startTime;
  doc["end_time"] = endTime;
  doc["duration"] = eventDuration;
  doc["api_key"] = apiKey;

  String jsonString;
  serializeJson(doc, jsonString);

  // Send HTTP POST
  HTTPClient http;
  http.begin(serverURL);
  http.addHeader("Content-Type", "application/json");

  Serial.println("🌐 Sending data to server:");
  Serial.println(jsonString);

  int httpResponseCode = http.POST(jsonString);

  if (httpResponseCode > 0) {
    String response = http.getString();
    Serial.printf("✅ Data sent successfully (Code: %d)\n", httpResponseCode);
    Serial.println("Server response: " + response);

    // Reset event tracking
    eventPending = false;
    eventDuration = 0;
  } else {
    Serial.printf("❌ HTTP POST failed (Code: %d)\n", httpResponseCode);
  }

  http.end();
}

String getISOTimestamp(unsigned long timeMillis) {

  // Get current time in seconds since epoch (simulated)
  unsigned long currentSeconds = 1672531200 + (timeMillis / 1000); 

  // Convert to date/time components (simplified)
  int year = 2023;
  int month = 1;
  int day = 1 + (currentSeconds / 86400) % 365; 
  int hour = (currentSeconds / 3600) % 24;
  int minute = (currentSeconds / 60) % 60;
  int second = currentSeconds % 60;

  char buffer[25];
  snprintf(buffer, sizeof(buffer), "%04d-%02d-%02dT%02d:%02d:%02d.000Z",
           year, month, day, hour, minute, second);
  return String(buffer);
}

// ========================================
// UTILITY FUNCTIONS
// ========================================

void blinkLED(int times, int delayMs) {
  for (int i = 0; i < times; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(delayMs);
    digitalWrite(LED_PIN, LOW);
    delay(delayMs);
  }
}

void updateStatusLED() {
  static unsigned long lastBlink = 0;
  static bool ledState = false;

  if (WiFi.status() == WL_CONNECTED) {
    // Solid ON when connected and no events pending
    digitalWrite(LED_PIN, eventPending ? LOW : HIGH);
  } else {
    // Slow blink when disconnected
    if (millis() - lastBlink > 500) {
      ledState = !ledState;
      digitalWrite(LED_PIN, ledState);
      lastBlink = millis();
    }
  }
}
